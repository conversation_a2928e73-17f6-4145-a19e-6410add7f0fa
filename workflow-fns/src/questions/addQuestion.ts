import type { InterfaceQuestion, Macro, TextBoxQuestion } from '@ayx/workflow-schemas';
import type { Path } from 'ramda';
import { curry, isNil, isNotNil } from 'ramda';

import { findQuestionPath } from '../iterations/internal/questionsDFS';
import { invariant } from '../utils/invariant';
import { QUESTIONS_PATH, RUNTIME_PROPERTIES_PATH } from '../utils/runtimePropertiesPaths';

import { insertQuestionIntoSource } from './internal/insertQuestionIntoSource';
import type { AddQuestionProps } from './questions.types';

const getParentQuestionPath = (type: string, parentId: string | undefined, macro: Macro): Path => {
  // Tabs also go in the base array
  if (type === 'Tab') {
    return [...RUNTIME_PROPERTIES_PATH, ...QUESTIONS_PATH];
  }

  // if no parentId passed, place in first Tab
  if (isNil(parentId)) {
    const firstTabPath = [...RUNTIME_PROPERTIES_PATH, ...QUESTIONS_PATH, 0, ...QUESTIONS_PATH];
    // TODO: invariant if firstTabPath doesn't exist
    return firstTabPath;
  }

  // else, place in parentId children
  const parentPath = findQuestionPath(parentId, macro);
  invariant(isNotNil(parentPath), `Cannot add new question to questionId "${parentId}"`);

  // TODO: invariant on parentQuestion that it is allowed to _have_ children or not
  return parentPath.concat(QUESTIONS_PATH);
};

const makeQuestion = (
  props: AddQuestionProps,
): Omit<InterfaceQuestion, 'Description'> | Omit<TextBoxQuestion, 'Description'> => {
  const newQuestion: Omit<InterfaceQuestion, 'Description'> & Partial<TextBoxQuestion> = {
    Name: props.name,
    ToolId: { '@value': props.toolId },
    Type: props.type,
    // props.configuration has more on it than just `Description`, but `Description` always expected
    // the type cast here is to satisfy typing restraints
    ...(props.configuration as object),
  };

  if (props.type === 'Tab') {
    newQuestion.Questions = { Question: [] };
  }

  if (props.type === 'TextBox') {
    // Set TextBox properties from configuration or use defaults
    const config = props.configuration as Record<string, unknown>;
    newQuestion.Default = (config?.Default as string) || '10';
    newQuestion.Hidden = (config?.Hidden as { '@value': string }) || { '@value': 'False' };
    newQuestion.Multiline = (config?.Multiline as { '@value': string }) || { '@value': 'False' };
    newQuestion.Password = (config?.Password as { '@value': string }) || { '@value': 'False' };
  }
  return newQuestion;
};

/**
 * add a question to the macro questions list
 *
 * @internal
 * @group Questions
 */
export const addQuestion: {
  (actionProps: AddQuestionProps): (macro: Macro) => Macro;
  (actionProps: AddQuestionProps, macro: Macro): Macro;
} = curry((props: AddQuestionProps, macro: Macro): Macro => {
  invariant(isNotNil(macro.Properties.RuntimeProperties), 'RuntimeProperties must be on a Macro');

  const isKnownQuestionPath = findQuestionPath(props.toolId, macro);
  invariant(isNil(isKnownQuestionPath), `Question already exists for toolId ${props.toolId}`);

  const question = makeQuestion(props);
  const parentPath = getParentQuestionPath(props.type, props.parentId, macro);

  invariant(parentPath, `Unable to find a path to parent question, ${props.parentId}`);

  return insertQuestionIntoSource(question, parentPath, macro);
});
