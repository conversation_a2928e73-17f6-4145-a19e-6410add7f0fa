import type { InterfaceQuestion, Macro, TextBoxQuestion } from '@ayx/workflow-schemas';
import { invariant } from '@formatjs/intl/src/utils';
import { curry, isNotNil, lensPath, set, view } from 'ramda';

import { findQuestionPath } from '../iterations/internal/questionsDFS';

import type { UpdateQuestionProps } from './questions.types';

/**
 * Update a question within the Macro.
 * This uses the lens to work on the flattened (read-only) view of the questions,
 * applies the update, and then reconstructs the Macro with the updated nested tree.
 *
 * @internal
 * @group Questions
 */
export const updateQuestion: {
  (toolId: string): {
    (props: UpdateQuestionProps): (macro: Macro) => Macro;
    (props: UpdateQuestionProps, macro: Macro): Macro;
  };
  (toolId: string, props: UpdateQuestionProps): (macro: Macro) => Macro;
  (toolId: string, props: UpdateQuestionProps, macro: Macro): Macro;
} = curry((toolId: string, props: UpdateQuestionProps, macro: Macro): Macro => {
  const questionPath = findQuestionPath(toolId, macro);
  invariant(isNotNil(questionPath), `Cannot update question for nodeId "${toolId}". It does not exist in the macro.`);

  const questionLens = lensPath<Macro, Omit<InterfaceQuestion, 'Description'> | Omit<TextBoxQuestion, 'Description'>>(
    questionPath,
  );

  const question = view(questionLens, macro);

  // do this new every time because of how configuration needs to be spread out
  const newQuestion: Omit<InterfaceQuestion, 'Description'> & Partial<TextBoxQuestion> = {
    Name: props.name ?? question.Name,
    ToolId: question.ToolId,
    Type: question.Type,
    // TODO: WorkflowNode.configuration is going to be updated from `unknown` to `object` in a future major and this will fix itself
    ...(props.configuration as object),
  };
  if (question.Type === 'TextBox') {
    // Set TextBox properties from configuration or preserve existing values
    const config = props.configuration as Record<string, unknown>;
    const existing = question as TextBoxQuestion;
    newQuestion.Default = (config?.Default as string) ?? existing.Default ?? '10';
    newQuestion.Hidden = (config?.Hidden as { '@value': string }) ?? existing.Hidden ?? { '@value': 'False' };
    newQuestion.Multiline = (config?.Multiline as { '@value': string }) ?? existing.Multiline ?? { '@value': 'False' };
    newQuestion.Password = (config?.Password as { '@value': string }) ?? existing.Password ?? { '@value': 'False' };
  }

  // only add if exists
  if (isNotNil(question.Questions)) {
    newQuestion.Questions = question.Questions;
  }

  return set(questionLens, newQuestion, macro);
});
