import { ACTION_INTERFACE_PLUGIN_ID, getStaticConfig } from '@ayx/tools-schemas';
import type { StaticConfig, Workflow } from '@ayx/workflow-schemas';
import { isMacro } from '@ayx/workflow-schemas';
import { curry, equals, isNotNil, view } from 'ramda';
import type { Placeholder } from 'ramda';

import { getActions } from '../actions/getActions';
import { updateAction } from '../actions/updateAction';
import { findNodePath } from '../iterations/internal/nodesDFS';
import { pluginId as pluginIdLens } from '../lenses/node';
import { findNode } from '../nodes/findNode';
import { invariant } from '../utils/invariant';

import type { AddConnectionProps, FnsConnection } from './connection.types';
import { toWorkflowConnection } from './connection.utils';
import { getIncomingConnections, getOutgoingConnections } from './getConnectionsForNode';
import { insertConnection } from './internal/insertConnection';

const determineHighestGeneratedConnectionNameNumber = (incomingConnections: FnsConnection[]) => {
  const rHashSingleDigit = /^#(\d+)$/;
  const exec = rHashSingleDigit.exec.bind(rHashSingleDigit);

  const names = incomingConnections.map(x => x.name).filter(isNotNil);
  const connectionNumbers = names
    .map(exec)
    .filter(isNotNil)
    .map(r => parseInt(r[1], 10));

  const highest = Math.max(...connectionNumbers);

  return equals(-Infinity, highest) ? 0 : highest;
};

const generateMultiInputConnectionName = (
  toId: string,
  toPort: string,
  toStaticConfig: StaticConfig,
  workflow: Workflow,
) => {
  const destinationPort = toStaticConfig.inputConnections.find(conn => conn.name === toPort);

  invariant(
    destinationPort,
    `Connection name "${toPort}" was not found on the staticConfig for "${toStaticConfig.id}"`,
  );

  if (!destinationPort.multiple) return undefined;

  const incomingConnections = getIncomingConnections(toId, workflow);
  const highest = determineHighestGeneratedConnectionNameNumber(incomingConnections);
  return `#${highest + 1}`;
};

/**
 * Add a connection between nodes in a workflow
 *
 * @public
 * @group Connections
 */
export const addConnection: {
  (props: AddConnectionProps): (workflow: Workflow) => Workflow;
  (__: Placeholder, workflow: Workflow): (props: AddConnectionProps) => Workflow;
  (props: AddConnectionProps, workflow: Workflow): Workflow;
} = curry((props: AddConnectionProps, workflow: Workflow): Workflow => {
  const { fromId, fromPort, toId, toPort } = props;

  const fromNodePath = findNodePath(fromId, workflow);
  invariant(fromNodePath, `Cannot add connection to fromId "${fromId}", no Node with that id exists.`);

  const toNodePath = findNodePath(toId, workflow);
  invariant(toNodePath, `Cannot add connection to toId "${toId}", no Node with that id exists.`);

  const fromNodePluginId = view(pluginIdLens(fromNodePath), workflow);
  const fromStaticConfig =
    getStaticConfig(fromNodePluginId) ?? props.adHocStaticConfigs?.find(sc => sc.id === fromNodePluginId);

  // assume that since the node already exists in the workflow, that the staticConfig exists for it
  const hasFromPort = fromStaticConfig?.outputConnections.some(c => c.name === fromPort);
  invariant(hasFromPort, `Cannot add connection to fromId "${fromId}", it does not have an output port "${fromPort}".`);

  const pluginId = view(pluginIdLens(toNodePath), workflow);
  const toStaticConfig = getStaticConfig(pluginId) ?? props.adHocStaticConfigs?.find(sc => sc.id === pluginId);

  invariant(toStaticConfig, `Unable to find the staticConfig for pluginId ${pluginIdLens(toNodePath)}`);

  // assume that since the node already exists in the workflow, that the staticConfig exists for it
  const toPortStaticConfig = toStaticConfig?.inputConnections.find(c => c.name === toPort);
  invariant(toPortStaticConfig, `Cannot add connection to toId "${toId}", it does not have an input port "${toPort}".`);

  // for connections without a name, check if we need to generate one
  // this is specifically for multi-input anchors
  const generatedName =
    toPortStaticConfig.multiple && !props.name
      ? generateMultiInputConnectionName(toId, toPort, toStaticConfig, workflow)
      : props.name;

  const newConnection = toWorkflowConnection(generatedName ? { ...props, name: generatedName } : props);
  const connectionProps = generatedName ? { ...props, name: generatedName } : props;

  const updatedWorkflow = insertConnection(newConnection, workflow);

  // Update ActionTool destinations if this connection involves an ActionTool
  if (isMacro(updatedWorkflow)) {
    const fromNode = findNode(connectionProps.fromId, updatedWorkflow);
    if (fromNode?.pluginId === ACTION_INTERFACE_PLUGIN_ID && connectionProps.fromPort === 'Action') {
      // Get all outgoing connections from this ActionTool
      const outgoingConnections = getOutgoingConnections(connectionProps.fromId, updatedWorkflow);
      const actionConnection = outgoingConnections.find(conn => conn.fromPort === 'Action');

      // Determine the new destination
      const newDestination = actionConnection ? `${actionConnection.toId}/FormulaFields/FormulaField/@expression` : '';

      // Update the destination for all actions of this ActionTool
      const actions = getActions(updatedWorkflow);
      const actionToolActions = actions.noCondition.trueActions.filter(
        action => action.toolId === connectionProps.fromId,
      );

      let finalWorkflow = updatedWorkflow;
      actionToolActions.forEach((_, index) => {
        finalWorkflow = updateAction(
          { index, toolId: connectionProps.fromId },
          { destination: newDestination },
          finalWorkflow,
        );
      });

      return finalWorkflow;
    }
  }

  return updatedWorkflow;
});
