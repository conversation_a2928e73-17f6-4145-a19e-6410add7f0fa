import { ACTION_INTERFACE_PLUGIN_ID } from '@ayx/tools-schemas';
import type { Macro } from '@ayx/workflow-schemas';
import { isMacro } from '@ayx/workflow-schemas';
import { curry, isNil } from 'ramda';
import type { Placeholder } from 'ramda';

import { getActions } from '../actions/getActions';
import { updateAction } from '../actions/updateAction';
import { findNode } from '../nodes/findNode';

import type { FnsConnection } from './connection.types';
import { getOutgoingConnections } from './getConnectionsForNode';

/**
 * Update ActionTool destinations when connections are added or removed
 * 
 * @internal
 * @group Connections
 */
export const updateActionToolDestinations: {
  (connection: FnsConnection): (workflow: Macro) => Macro;
  (__: Placeholder, workflow: Macro): (connection: FnsConnection) => Macro;
  (connection: FnsConnection, workflow: Macro): Macro;
} = curry((connection: FnsConnection, workflow: Macro): Macro => {
  if (!isMacro(workflow)) {
    return workflow;
  }

  // Check if the connection is from an ActionTool's Action anchor
  const fromNode = findNode(connection.fromId, workflow);
  if (!fromNode || fromNode.pluginId !== ACTION_INTERFACE_PLUGIN_ID || connection.fromPort !== 'Action') {
    return workflow;
  }

  // Get all outgoing connections from this ActionTool
  const outgoingConnections = getOutgoingConnections(connection.fromId, workflow);
  
  // Find the first connection from the Action anchor (there should only be one)
  const actionConnection = outgoingConnections.find(conn => conn.fromPort === 'Action');
  
  // Determine the new destination
  const newDestination = actionConnection 
    ? `${actionConnection.toId}/FormulaFields/FormulaField/@expression`
    : '';

  // Get current actions for this ActionTool
  const actions = getActions(workflow);
  const actionToolActions = actions.noCondition.trueActions.filter(
    action => action.toolId === connection.fromId
  );

  // Update the destination for all actions of this ActionTool
  let updatedWorkflow = workflow;
  actionToolActions.forEach((action, index) => {
    updatedWorkflow = updateAction(
      { toolId: connection.fromId, index },
      { destination: newDestination },
      updatedWorkflow
    );
  });

  return updatedWorkflow;
});
