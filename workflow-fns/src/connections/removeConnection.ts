import type { Workflow } from '@ayx/workflow-schemas';
import { isMacro } from '@ayx/workflow-schemas';
import type { Placeholder } from 'ramda';
import { curry, isNil, over, remove } from 'ramda';

import { getConnectionIndex } from '../iterations/connectionIndexes';

import type { FnsConnection } from './connection.types';
import { connectionsLens, toWorkflowConnection } from './connection.utils';
import { getConnectionsForNode } from './getConnectionsForNode';
import { updateActionToolDestinations } from './updateActionToolDestinations';

/**
 * Remove a connection for a workflow
 *
 * @public
 * @group Connections
 */
export const removeConnection: {
  (connection: Omit<FnsConnection, 'name'>): (workflow: Workflow) => Workflow;
  (__: Placeholder, workflow: Workflow): (connection: Omit<FnsConnection, 'name'>) => Workflow;
  (connection: Omit<FnsConnection, 'name'>, workflow: Workflow): Workflow;
} = curry((connection: Omit<FnsConnection, 'name'>, workflow: Workflow): Workflow => {
  const connectionIndex = getConnectionIndex(toWorkflowConnection(connection), workflow);

  if (isNil(connectionIndex)) {
    return workflow;
  }

  const updatedWorkflow = over(connectionsLens, remove(connectionIndex, 1), workflow);

  // Update ActionTool destinations if this connection removal involves an ActionTool
  return isMacro(updatedWorkflow) ? updateActionToolDestinations(connection, updatedWorkflow) : updatedWorkflow;
});

/**
 * Remove all connections for a nodeId of a workflow
 *
 * @public
 * @group Connections
 */
export const removeConnectionsForNode: {
  (nodeId: string, workflow: Workflow): Workflow;
  (__: Placeholder, workflow: Workflow): (nodeId: string) => Workflow;
  (nodeId: string): (workflow: Workflow) => Workflow;
} = curry((nodeId: string, workflow: Workflow) =>
  getConnectionsForNode(nodeId, workflow).reduce((wf, conn) => removeConnection(conn, wf), workflow),
);
