import { ACTION_INTERFACE_PLUGIN_ID } from '@ayx/tools-schemas';
import type { Workflow } from '@ayx/workflow-schemas';
import { isM<PERSON><PERSON> } from '@ayx/workflow-schemas';
import type { Placeholder } from 'ramda';
import { curry, isNil, over, remove } from 'ramda';

import { getActions } from '../actions/getActions';
import { updateAction } from '../actions/updateAction';
import { getConnectionIndex } from '../iterations/connectionIndexes';
import { findNode } from '../nodes/findNode';

import type { FnsConnection } from './connection.types';
import { connectionsLens, toWorkflowConnection } from './connection.utils';
import { getConnectionsForNode } from './getConnectionsForNode';

/**
 * Remove a connection for a workflow
 *
 * @public
 * @group Connections
 */
export const removeConnection: {
  (connection: Omit<FnsConnection, 'name'>): (workflow: Workflow) => Workflow;
  (__: Placeholder, workflow: Workflow): (connection: Omit<FnsConnection, 'name'>) => Workflow;
  (connection: Omit<FnsConnection, 'name'>, workflow: Workflow): Workflow;
} = curry((connection: Omit<FnsConnection, 'name'>, workflow: Workflow): Workflow => {
  const connectionIndex = getConnectionIndex(toWorkflowConnection(connection), workflow);

  if (isNil(connectionIndex)) {
    return workflow;
  }

  const updatedWorkflow = over(connectionsLens, remove(connectionIndex, 1), workflow);

  // Update ActionTool destinations if this connection removal involves an ActionTool
  if (isMacro(updatedWorkflow)) {
    const fromNode = findNode(connection.fromId, updatedWorkflow);
    if (fromNode?.pluginId === ACTION_INTERFACE_PLUGIN_ID && connection.fromPort === 'Action') {
      const newDestination = '';
      const actions = getActions(updatedWorkflow);
      const actionToolActions = actions.noCondition.trueActions.filter(action => action.toolId === connection.fromId);

      let finalWorkflow = updatedWorkflow;
      actionToolActions.forEach((_, index) => {
        finalWorkflow = updateAction(
          { index, toolId: connection.fromId },
          { destination: newDestination },
          finalWorkflow,
        );
      });

      return finalWorkflow;
    }
  }

  return updatedWorkflow;
});

/**
 * Remove all connections for a nodeId of a workflow
 *
 * @public
 * @group Connections
 */
export const removeConnectionsForNode: {
  (nodeId: string, workflow: Workflow): Workflow;
  (__: Placeholder, workflow: Workflow): (nodeId: string) => Workflow;
  (nodeId: string): (workflow: Workflow) => Workflow;
} = curry((nodeId: string, workflow: Workflow) =>
  getConnectionsForNode(nodeId, workflow).reduce((wf, conn) => removeConnection(conn, wf), workflow),
);
