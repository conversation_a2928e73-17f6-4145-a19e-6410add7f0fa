import type { Macro, RuntimeAction } from '@ayx/workflow-schemas';
import { assocPath, curry, isNil, lensPath, view } from 'ramda';

import { conditionActionsPath } from '../macro/lenses/runtimeProperties/utils/conditionsActionsPath';
import { invariant } from '../utils/invariant';
import { NO_CONDITION_ACTIONS_PATH } from '../utils/runtimePropertiesPaths';

import type { ConditionType } from './types/actions.general.types';

/**
 * Remove a runtime action from the Actions various lists
 *
 * @internal
 * @group Actions
 */
export const removeAction = curry(
  (
    {
      toolId,
      condition,
    }: {
      condition?: ConditionType;
      toolId: string;
    },
    macro: Macro,
  ): Macro => {
    const actionPath = isNil(condition) ? NO_CONDITION_ACTIONS_PATH : conditionActionsPath(toolId, condition, macro);
    invariant(actionPath, `A condition does not exist for toolId ${toolId}`);

    const filteredActions = view(lensPath<Macro, RuntimeAction[]>(actionPath), macro).filter(
      a => a.ToolId['@value'] !== toolId,
    );

    return assocPath(actionPath, filteredActions, macro);
  },
);
