import { ACTION_INTERFACE_PLUGIN_ID, TEXT_BOX_QUESTION_PLUGIN_ID } from '@ayx/tools-schemas';
import { QUESTIONS_TAB_PLUGIN } from '@ayx/workflow-schemas';
import type { Macro } from '@ayx/workflow-schemas';
import { flow, isNotNil } from 'ramda';

import { addAction } from '../../actions/addAction';
import { addConstant } from '../../constants/addConstant';
import { isIoInterfaceTool, makeDefaultQuestionName, pluginToQuestionType } from '../../macro/macro.utils';
import type { SupportedInterfacePluginId } from '../../macro/macro.utils';
import { addQuestion } from '../../questions/addQuestion';
import { invariant } from '../../utils/invariant';
import { getAllNodes } from '../getAllNodes';
import type { AddNodeProps } from '../node.types';

import { addNodeBase } from './addNodeBase';

type AddInterfaceToolProps = Omit<AddNodeProps, 'pluginId'> & { pluginId: SupportedInterfacePluginId };

// Helper function to add default action for ActionTool
const addDefaultActionToolAction = (toolId: string, macroToUpdate: Macro): Macro =>
  addAction(
    {
      description: 'Default Action',
      // Empty by default, will be updated when output anchor is connected
      destination: '',
      mode: 'Simple',
      replace: 'True',
      replaceText: '20',
      toolId,
      type: 'UpdateValue',
      variable: '',
    },
    macroToUpdate,
  );

/**
 * When adding a Macro Input or Output tool, a few things need to happen.
 *
 * 1) Add the tool like we do with any tool.
 * 2) Add a corresponding Constant in the Properties
 * 3) Add to the Wizard openOutputTools list
 * 4) Add a child question to the Tab Question, "Questions", corresponding to the new tool
 * @internal
 */
export const addInterfaceTool = (props: AddInterfaceToolProps, macro: Macro): Macro => {
  // TODO: should this be lifted up to `isMacro()` ??
  invariant(isNotNil(macro.Properties.RuntimeProperties), 'RuntimeProperties must be on a Macro');

  // TODO: should this auto-add a `Tab` Node and <Question> if missing? on the off chance that the json is malformed?
  const tabQuestionId = getAllNodes(macro).find(n => n.pluginId === QUESTIONS_TAB_PLUGIN)?.id;
  invariant(isNotNil(tabQuestionId), `This macro is missing a ${QUESTIONS_TAB_PLUGIN} node id`);

  const { configuration, ...rest } = props;

  // use `||` over `??` here because we want to replace emptyString as well as undefined/null
  const macroName = props.annotationName || makeDefaultQuestionName(props.id, props.pluginId);

  // ioInterfaceTools are special in that they config goes in the Node like Data Tools
  // but "description" still needs to be in the question.configuration
  let questionConfiguration;
  if (isIoInterfaceTool(props.pluginId)) {
    questionConfiguration = {
      Description: macroName,
    };
  } else if (props.pluginId === TEXT_BOX_QUESTION_PLUGIN_ID) {
    questionConfiguration = {
      Description: 'Input',
      ...(props.configuration as Record<string, unknown>),
    };
  } else {
    questionConfiguration = props.configuration;
  }

  const updatedMacro = flow(macro, [
    // Add the Wizard Property
    // NOTE: Commented out because these props at not necessary for Macros, only AnalyticApps, though Desktop adds them for both
    // addOpenOutputTools([{ selected: true, toolId: props.id }]),

    // Add the Constant (skip for ActionTool)
    // NOTE: commented out because not needed for MacroInput/MacroOutput, will put back once we support more InterfaceTools
    ...(props.pluginId !== ACTION_INTERFACE_PLUGIN_ID
      ? [
          addConstant({
            isNumeric: false,
            name: macroName,
            namespace: 'Question',
            value: '',
          }),
        ]
      : []),

    // Add the Question to the Tab (skip for ActionTool)
    ...(props.pluginId !== ACTION_INTERFACE_PLUGIN_ID
      ? [
          addQuestion({
            configuration: questionConfiguration,
            name: macroName,
            parentId: props.questionId || tabQuestionId,
            toolId: props.id,
            type: pluginToQuestionType[props.pluginId],
          }),
        ]
      : []),

    // add the Node
    (m: Macro) =>
      addNodeBase(
        {
          ...rest,
          // only MacroInput/MacroOutput has its config in the Node, rest go in the Question
          configuration: isIoInterfaceTool(props.pluginId) ? configuration : undefined,
        },
        m,
      ),
  ]) as Macro;

  // Add action by default for ActionTool
  if (props.pluginId === ACTION_INTERFACE_PLUGIN_ID) {
    return addDefaultActionToolAction(props.id, updatedMacro);
  }

  return updatedMacro;
};
