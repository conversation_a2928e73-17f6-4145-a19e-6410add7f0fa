import { ACTION_INTERFACE_PLUGIN_ID } from '@ayx/tools-schemas';
import type { InterfaceQuestion, Macro, TextBoxQuestion } from '@ayx/workflow-schemas';
import type { Path } from 'ramda';
import { flow, isEmpty, isNotNil, path } from 'ramda';

import { findQuestionPath } from '../../iterations/internal/questionsDFS';
import { isIoInterfaceTool, makeDefaultQuestionName } from '../../macro/macro.utils';
import type { SupportedInterfacePluginId } from '../../macro/macro.utils';
import { updateQuestion } from '../../questions/updateQuestion';
import { invariant } from '../../utils/invariant';
import type { FnsNode, UpdateNodeProps } from '../node.types';

import { reduceUpdateLensProps } from './node.utils';

/** @internal */
export const updateInterfaceTool = (nodePath: Path, node: FnsNode, props: UpdateNodeProps, macro: Macro): Macro => {
  // Skip question updates for ActionTool since they don't have questions in the workflow XML
  if (node.pluginId === ACTION_INTERFACE_PLUGIN_ID) {
    const { configuration: nodeConfiguration, ...otherProps } = props;
    return reduceUpdateLensProps(nodePath, otherProps, macro) as Macro;
  }

  const questionPath = findQuestionPath(node.id, macro);
  invariant(isNotNil(questionPath), `Cannot update question for nodeId "${node.id}". It does not exist in the macro.`);
  const questionObj = path<InterfaceQuestion | TextBoxQuestion>(questionPath, macro)!;

  // if annotationName is emptyString, revert it to the defaultQuestionName
  // don't do this for `undefined`, as `undefined` is processed as "don't update"
  const macroName =
    isNotNil(props.annotationName) && isEmpty(props.annotationName)
      ? makeDefaultQuestionName(node.id, node.pluginId as SupportedInterfacePluginId)
      : props.annotationName;

  // For MacroInput/Output, we ignore incoming configuration
  // instead use `{ Description: string }` using existing value
  // else, use the full configuration
  const questionConfiguration = isIoInterfaceTool(node.pluginId)
    ? { Description: questionObj?.Description }
    : props.configuration;

  // and vice-versa for the nodeConfiguration, use incoming configuration when MacroInput/Output, emptyObj otherwise
  const { configuration: nodeConfiguration, ...otherProps } = props;
  const interfaceToolProps = isIoInterfaceTool(node.pluginId) && isNotNil(nodeConfiguration) ? props : otherProps;

  return flow(macro, [
    // TODO: add back in once we support the InterfaceTools that add Constants
    // updateConstant(node.annotationText, { name: macroName }),
    updateQuestion(node.id, { configuration: questionConfiguration, name: macroName }),
    wf => reduceUpdateLensProps(nodePath, interfaceToolProps, wf),
  ]) as Macro;
};
