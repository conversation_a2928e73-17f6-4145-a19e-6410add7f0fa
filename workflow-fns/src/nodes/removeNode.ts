import { ACTION_INTERFACE_PLUGIN_ID } from '@ayx/tools-schemas';
import { isMacro } from '@ayx/workflow-schemas';
import type { Workflow } from '@ayx/workflow-schemas';
import type { Placeholder } from 'ramda';
import { curry, flow, view } from 'ramda';

import { removeAction } from '../actions/removeAction';
import { removeConstant } from '../constants/removeConstant';
import { findNodePath } from '../iterations/internal/nodesDFS';
import { pluginId as pluginIdLens } from '../lenses/node/pluginId';
import { isInterfaceTool, makeDefaultQuestionName } from '../macro/macro.utils';
import type { SupportedInterfacePluginId } from '../macro/macro.utils';
import { removeQuestion } from '../questions/removeQuestion';
import { invariant } from '../utils/invariant';

import { removeNodeFromNodesArray } from './internal/node.utils';

/**
 * Add a new container to a workflow.
 * Warning: if you do this for a container you will lose all of its children
 *
 * @public
 * @group Nodes
 */
export const removeNode: {
  (toolId: string): (workflow: Workflow) => Workflow;
  (__: Placeholder, workflow: Workflow): (toolId: string) => Workflow;
  (toolId: string, workflow: Workflow): Workflow;
} = curry((toolId: string, workflow: Workflow): Workflow => {
  const nodePath = findNodePath(toolId, workflow);
  invariant(nodePath, `Cannot remove node. Node with id "${toolId}" not found on workflow.`);

  // TODO: WorkflowToolkit only removes only the node.
  // should this function be enhanced to automatically:
  // * all connections to/from the node
  // * recursively remove children and links
  // Harris says: I have this planned for the next Major release of `workflow-fns`

  const pluginId = view(pluginIdLens(nodePath), workflow);

  if (isInterfaceTool(pluginId) && isMacro(workflow)) {
    return flow(workflow, [
      // TODO: add back in once we support the InterfaceTools that add Constants
      // Skip constant removal for ActionTool since it doesn't create constants
      ...(pluginId !== ACTION_INTERFACE_PLUGIN_ID
        ? [removeConstant(makeDefaultQuestionName(toolId, pluginId as SupportedInterfacePluginId))]
        : []),
      removeAction({ toolId }),
      // Skip question removal for ActionTool since it doesn't create questions
      ...(pluginId !== ACTION_INTERFACE_PLUGIN_ID ? [removeQuestion(toolId)] : []),
      removeNodeFromNodesArray(nodePath),
    ]);
  }

  return removeNodeFromNodesArray(nodePath, workflow);
});
